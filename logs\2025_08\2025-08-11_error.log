2025-08-11 10:59:20 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: 0d36c210-cf14-4739-b6c0-fdca7e888375 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:02:54 | ERROR    | app.api.routes:check_compliance_simple:166 | 简化合规性检查失败 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:02:54 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: 4b252d29-17ad-46ac-9ff4-e6b595029ed7 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:06:55 | ERROR    | app.api.routes:check_compliance_simple:166 | 简化合规性检查失败 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:06:55 | ERROR    | app.middleware.validation:dispatch:55 | 请求验证异常 | ID: d4da9454-3c15-48e9-9ebd-087049196ec8 | 错误: 1 validation error for ComplianceCheckRequest
project_category
  Input should be '政府采购', '非政府采购' or '非依法招标' [type=enum, input_value='依法招标', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/enum
2025-08-11 11:42:46 | ERROR    | app.services.result_processor:_verify_processing_integrity:832 | 完整性验证 | 检查结果验证 | ID: ab7abca3-cf91-48a8-bc22-4d1b14d9497c | 状态: 灾难性丢失 | 输入: 1 | 输出: 0 | 丢失: 1 (100.0%)
2025-08-11 14:35:09 | ERROR    | app.core.logger:__exit__:421 | 操作 带完整性检查的结果聚合 发生异常: 'ComplianceCheckResponse' object has no attribute 'sensitiveWordsArr'
2025-08-11 14:37:06 | ERROR    | app.core.logger:__exit__:421 | 操作 带完整性检查的结果聚合 发生异常: 'ComplianceCheckResponse' object has no attribute 'sensitiveWordsArr'
2025-08-11 16:17:34 | ERROR    | app.core.logger:__exit__:421 | 操作 MarkItDown转换 发生异常: File conversion failed after 1 attempts:
 - DocxConverter threw MissingDependencyException with message: DocxConverter recognized the input as a potential .docx file, but the dependencies needed to read .docx files have not been installed. To resolve this error, include the optional dependency [docx] or [all] when installing MarkItDown. For example:

* pip install markitdown[docx]
* pip install markitdown[all]
* pip install markitdown[docx, ...]
* etc.

2025-08-11 16:17:34 | ERROR    | app.services.file_processor_v2:convert_with_markitdown:342 | MarkItDown转换失败: File conversion failed after 1 attempts:
 - DocxConverter threw MissingDependencyException with message: DocxConverter recognized the input as a potential .docx file, but the dependencies needed to read .docx files have not been installed. To resolve this error, include the optional dependency [docx] or [all] when installing MarkItDown. For example:

* pip install markitdown[docx]
* pip install markitdown[all]
* pip install markitdown[docx, ...]
* etc.

2025-08-11 16:17:34 | ERROR    | app.core.logger:wrapper:452 | 函数调用失败: convert_with_markitdown | 耗时: 0.258秒 | 错误: MarkItDown转换失败: File conversion failed after 1 attempts:
 - DocxConverter threw MissingDependencyException with message: DocxConverter recognized the input as a potential .docx file, but the dependencies needed to read .docx files have not been installed. To resolve this error, include the optional dependency [docx] or [all] when installing MarkItDown. For example:

* pip install markitdown[docx]
* pip install markitdown[all]
* pip install markitdown[docx, ...]
* etc.
 (类型: .docx, 阶段: MarkItDown转换, 原因: File conversion failed after 1 attempts:
 - DocxConverter threw MissingDependencyException with message: DocxConverter recognized the input as a potential .docx file, but the dependencies needed to read .docx files have not been installed. To resolve this error, include the optional dependency [docx] or [all] when installing MarkItDown. For example:

* pip install markitdown[docx]
* pip install markitdown[all]
* pip install markitdown[docx, ...]
* etc.
)

"""
测试优化后的JSON处理功能
"""

import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.ai_model_service import AIModelService


def test_json_cleaning():
    """测试JSON清理功能"""
    
    # 创建AI模型服务实例
    ai_service = AIModelService()
    
    # 测试用例1：包含中文引号的JSON
    test_case_1 = '''
    {
      "checkResultArr": [
        {
          "quesType": "合规性",
          "quesDesc": "问题描述：文件中对同一事项（未提供人员社保证明）的处理方式存在矛盾。第四章要求"未提供视为不响应文件，按无效响应处理"，而第五章则规定"否则不予计分"。",
          "originalArr": [
            "本条款所涉及人员，供应商须提供其相关证件扫描件及为其缴纳的最近3个月社保证明材料，未提供视为不响应文件，按无效响应处理。"
          ],
          "point": "条款一致性验证",
          "advice": "建议统一处理方式。"
        }
      ]
    }
    '''
    
    print("测试用例1：包含中文引号的JSON")
    print("原始JSON长度:", len(test_case_1))
    
    try:
        cleaned = ai_service.clean_json_data(test_case_1)
        print("清理后JSON长度:", len(cleaned))
        
        # 尝试解析
        parsed = json.loads(cleaned)
        print("✅ JSON解析成功")
        print("检查结果数量:", len(parsed.get('checkResultArr', [])))
        
        if parsed.get('checkResultArr'):
            first_item = parsed['checkResultArr'][0]
            print("第一个问题类型:", first_item.get('quesType'))
            print("问题描述长度:", len(first_item.get('quesDesc', '')))
        
    except json.JSONDecodeError as e:
        print("❌ JSON解析失败:", str(e))
    except Exception as e:
        print("❌ 处理异常:", str(e))
    
    print("\n" + "="*50 + "\n")
    
    # 测试用例2：截断的JSON
    test_case_2 = '''
    {
      "checkResultArr": [
        {
          "quesType": "合规性",
          "quesDesc": "问题描述：招标代理机构要求投标人使用IE浏览器提交投标文件，但现代浏览器（如Chrome、Edge等）已不再支持IE的某些功能，这可能导致投标人无法正常提交投标文件，存在技术障碍。\\n原文位置：投标人须知前附表\\n原文内容：",
          "originalArr": [
            "投标人应使用IE浏览器登录电子招投标平台进行投标文件的制作和提交。"
          ],
          "point": "可操作性问题",
          "advice": "建议招标代理机构升级电子招投标平台，以支持Edge、Chrome等现代、安全的浏览器。"
        }
    '''
    
    print("测试用例2：截断的JSON")
    print("原始JSON长度:", len(test_case_2))
    
    try:
        cleaned = ai_service.clean_json_data(test_case_2)
        print("清理后JSON长度:", len(cleaned))
        
        # 尝试解析
        parsed = json.loads(cleaned)
        print("✅ JSON解析成功")
        print("检查结果数量:", len(parsed.get('checkResultArr', [])))
        
    except json.JSONDecodeError as e:
        print("❌ JSON解析失败:", str(e))
    except Exception as e:
        print("❌ 处理异常:", str(e))
    
    print("\n" + "="*50 + "\n")
    
    # 测试用例3：空响应
    test_case_3 = ""
    
    print("测试用例3：空响应")
    try:
        cleaned = ai_service.clean_json_data(test_case_3)
        parsed = json.loads(cleaned)
        print("✅ 空响应处理成功")
        print("返回结果:", parsed)
        
    except Exception as e:
        print("❌ 空响应处理失败:", str(e))


if __name__ == "__main__":
    test_json_cleaning()
